version: '3.8'

services:
  # Your main JavaScript server
  vyoo-server:
    build: 
      context: .
      dockerfile: Dockerfile
      args:
        - PORT=${PORT:-8081}
    ports:
      - "${SERVER_PORT:-3000}:${PORT:-8081}"
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - PORT=${PORT:-8081}
      - MONGODB_URI=mongodb://mongodb:27017/vyoo-server
      - RTMP_BASE_URL=rtmp://rtmp-server:1935/live
      - RTMP_LIVE_URL=rtmp://rtmp-server:1935/live
      - HLS_BASE_URL=http://rtmp-server:8080/hls
      - LIVE2_CONTAINER_NAME=rtmp-server
      - LIVE2_HTTP_PORT=8080
      - LIVE2_RTMP_PORT=1935
      - JWT_SECRET=${JWT_SECRET}
      - COOKIE_SECRET=${COOKIE_SECRET}
      - A<PERSON>_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION}
      - AWS_S3_BUCKET_NAME=${AWS_S3_BUCKET_NAME}
    depends_on:
      - rtmp-server
      - mongodb
    networks:
      - vyoo-network
    volumes:
      - ./logs:/app/logs:rw
      - app-data:/app/data
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${PORT:-8081}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 15s

  mongodb:
    image: mongo:latest
    ports:
      - "27017:27017"
    volumes:
      - mongo-data:/data/db
    networks:
      - vyoo-network
    restart: unless-stopped

  # Live2 RTMP/HLS Streaming Server - Comprehensive Fail-Safe System
  rtmp-server:
    build:
      context: ./live2
      dockerfile: Dockerfile
    ports:
      - "${RTMP_PORT:-1935}:1935"   # RTMP input port
      - "${HLS_PORT:-8080}:8080"   # HLS output port
    environment:
      # AWS Configuration for S3 Upload
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID:-********************}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY:-RdIHiTGoBlr/ncBxmie4/3a+3GS0gsMxJXi0Q13k}
      - AWS_REGION=${AWS_REGION:-ap-south-1}
      - S3_BUCKET=${AWS_S3_BUCKET_NAME:-vyooo-abr}
      # Fail-Safe System Configuration
      - S3_UPLOAD_ENABLED=true
      - S3_RECOVERY_ENABLED=true
      - MONITOR_ENABLED=true
      - LOG_LEVEL=INFO
      # HLS Configuration
      - HLS_DIR=/mnt/hls
      - MAX_RETRIES=5
      - RETRY_DELAY_SECONDS=2
      - FAILED_UPLOADS_LOG=/var/log/failed_uploads.log
      - RECOVERY_LOG=/var/log/s3_recovery.log
    volumes:
      - ./live2/conf/nginx.conf:/etc/nginx/nginx.conf:ro
      - rtmp-hls-data:/mnt/hls:rw
      - rtmp-dash-data:/mnt/dash:rw
    restart: unless-stopped
    networks:
      - vyoo-network
    shm_size: 2g
    deploy:
      resources:
        limits:
          memory: 8G
          cpus: '6.0'
        reservations:
          memory: 2G
          cpus: '3.0'
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/health || curl -f http://localhost:8080/stat || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 15s
    logging:
      driver: "json-file"
    ulimits:
      nofile:
        soft: 65536
        hard: 65536
    sysctls:
      - net.core.somaxconn=65535

networks:
  vyoo-network:
    driver: bridge
    driver_opts:
      com.docker.network.driver.mtu: 1500
    ipam:
      config:
        - subnet: **********/16

volumes:
  rtmp-hls-data:
    driver: local
  rtmp-dash-data:
    driver: local
  app-data:
    driver: local
  mongo-data:
    driver: local 