import ffmpeg from "fluent-ffmpeg";
import ffmpegStatic from "ffmpeg-static";
import fs from "fs";
import path from "path";
import ffprobe from "@ffprobe-installer/ffprobe";
import jwt, { Algorithm } from "jsonwebtoken";
import jwksClient from "jwks-rsa";

import { AWS_REGION, HLS_BUCKET, JWT_SECRET } from "../config/environment";
import { WEIGHTS } from "../constants/trendingWeights";
import { Response } from "express";
import axios from "axios";
import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
const parseString = require("xml2js").parseString;

const s3Client = new S3Client({
  region: AWS_REGION,
});
const APPLE_BASE_URL = "https://appleid.apple.com";
const JWKS_APPLE_URI = "/auth/keys";

/**
 * We Accept createdAt and return "Days ago"
 * @param createdAt
 * @returns Number
 */
export function calculateDaysAgo(createdAt: Date): number {
  const createdDate = new Date(createdAt);
  const currentDate = new Date();
  return Math.floor(
    (currentDate.getTime() - createdDate.getTime()) / (1000 * 60 * 60 * 24)
  );
}

ffmpeg.setFfmpegPath(ffmpegStatic!);
ffmpeg.setFfprobePath(ffprobe.path);

/**
 * Following function uses ffmpeg to retrieve Video Metadata.
 * @param buffer
 * @returns
 */
export const getVideoMetadataFromBuffer = async (
  buffer: any
): Promise<number> => {
  return new Promise((resolve, reject) => {
    const tempDir = path.join(__dirname, "tmp");
    const tempFilePath = path.join(tempDir, `${Date.now()}.mp4`);

    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }

    fs.writeFileSync(tempFilePath, buffer);

    ffmpeg.ffprobe(tempFilePath, (err: any, metadata: any) => {
      fs.unlinkSync(tempFilePath);
      if (err) {
        return reject(err);
      }
      resolve(metadata.format.duration);
    });
  });
};

/**
 * Generates a JWT token
 * @param payload - The data to encode in the token
 * @param expiresIn - The token's expiration time (default is '1h')
 * @returns The signed JWT token
 */
export const generateToken = (payload: object, expiresIn: string) => {
  if (!JWT_SECRET) {
    throw new Error("JWT_SECRET is not defined");
  }
  return jwt.sign(payload, JWT_SECRET, { expiresIn });
};

/**
 * We want to calculate base score with the help of weights. Weights can be adjusted later accordingly.
 * Decay factor makes sure new content gets the boost by implementing exponential decay.
 * At last we combine those two.
 * Base score: we measure the engagement and decay factor:Adjusts this score based on time
 * @param views We accept view count.
 * @param likes like count.
 * @param shares share count.
 * @param createdAt post date.
 * @returns {number} trending score for a stream.
 */
export const calculateTrendingScore = async (
  views: number,
  likes: number,
  shares: number,
  createdAt: Date
): Promise<number> => {
  const baseScore =
    WEIGHTS.VIEWS * views + WEIGHTS.LIKES * likes + WEIGHTS.SHARES * shares;
  const timeSincePosted =
    (Date.now() - new Date(createdAt).getTime()) / 3600000;
  const decayFactor = Math.exp(-timeSincePosted / WEIGHTS.DECAY_PERIOD);
  const trendingScore = baseScore * decayFactor;
  return trendingScore;
};

export const validateAppleToken = async (identityToken: string) => {
  const decodedToken = jwt.decode(identityToken, { complete: true });

  if (!decodedToken) throw new Error("Invalid identity token");

  const { kid, alg } = decodedToken.header;

  const client = jwksClient({ jwksUri: `${APPLE_BASE_URL}${JWKS_APPLE_URI}` });

  const signingKey = await client.getSigningKey(kid);

  const publicKey = signingKey.getPublicKey();

  const verifiedToken = jwt.verify(identityToken, publicKey, {
    algorithms: [alg as Algorithm],
  });

  if (typeof verifiedToken !== "object" || verifiedToken === null) {
    throw new Error("Invalid token payload");
  }

  if (verifiedToken.iss !== APPLE_BASE_URL) {
    throw new Error("Invalid issuer");
  }

  return verifiedToken;
};

export interface ApiResponse<T> {
  statusCode: number;
  success: boolean;
  message: string;
  data: T | null;
}
export const ResultDB = <T>(
  statusCode: number,
  success: boolean,
  message: string,
  data: T | null = null
): ApiResponse<T> => {
  return { statusCode, success, message, data };
};

export interface ResponseType {
  success: boolean;
  message: string;
  data: any;
}
export const SuccessResponse = <T>(
  res: Response,
  statusCode: number = 200,
  success: boolean,
  message: string,
  data: T | null = null
): Response<ResponseType> => {
  return res.status(statusCode).json({
    success: success,
    message,
    data,
  });
};

export const ErrorResponse = <T>(
  res: Response,
  statusCode: number = 500,
  success: boolean,
  message: string,
  data: T | null = null
): Response<ResponseType> => {
  return res.status(statusCode).json({
    success: success,
    message,
    data,
  });
};

/**
 * Calculate the total length of a video from an M3U8 playlist
 * @param streamKey The stream key to calculate the video length for
 * @returns The total duration of the video in seconds
 */
export const calculateVideoLengthFromM3U8 = async (
  streamKey: string
): Promise<number | null> => {
  try {
    const bucketName = "vyooo-hls"; // Your S3 bucket name
    const playlistUrl = `https://${bucketName}.s3.ap-south-1.amazonaws.com/hls/${streamKey}_720p/index.m3u8`;

    // 1. Fetch the M3U8 playlist
    const response = await axios.get(playlistUrl);

    const playlistContent = response.data;

    // 2. Parse the M3U8 playlist to sum up the durations
    const durationRegex = /#EXTINF:([\d.]+)/g;
    let match;
    let totalDuration = 0;

    while ((match = durationRegex.exec(playlistContent)) !== null) {
      totalDuration += parseFloat(match[1]);
    }

    return totalDuration; // Total duration in seconds
  } catch (error) {
    console.error("Error calculating video length:", error);
    return null;
  }
};

/**
 * Following function appends #EXT-X-ENDLIST to the end of the M3U8 playlist
 * @param streamKey
 */
export const appendEndlistToM3U8 = async (streamKey: string) => {
  const variantUrls = [
    `https://${HLS_BUCKET}.s3.ap-south-1.amazonaws.com/hls/${streamKey}_360p/index.m3u8`,
    `https://${HLS_BUCKET}.s3.ap-south-1.amazonaws.com/hls/${streamKey}_720p/index.m3u8`,
  ];

  for (const variantUrl of variantUrls) {
    try {
      console.log("variant url", variantUrl);
      // Download the current playlist
      const response = await axios.get(variantUrl);
      let playlistContent = response.data;

      // Check if `#EXT-X-ENDLIST` is already present
      if (!playlistContent.includes("#EXT-X-ENDLIST")) {
        playlistContent += "\n#EXT-X-ENDLIST\n";
      }

      // Save locally (or modify as needed)
      // const localFilePath = path.resolve("/tmp", path.basename(variantUrl));
      // fs.writeFileSync(localFilePath, playlistContent, "utf8");

      // Upload updated playlist back to S3
      const s3Key = new URL(variantUrl).pathname.substring(1);

      console.log(playlistContent);
      console.log("s3Key", s3Key);
      const command = new PutObjectCommand({
        Bucket: HLS_BUCKET,
        Key: s3Key,
        Body: playlistContent,
        ContentType: "application/vnd.apple.mpegurl",
      });

      const responseS3 = await s3Client.send(command);
      console.log("abcd", responseS3);
    } catch (error) {
      console.log(error);
      console.error(`Error appending #EXT-X-ENDLIST to ${variantUrl}:`, error);
    }
  }
};

// setInterval(async () => {
//   try {
//     const response = await axios.get("http://localhost:8080/stats");
//     // response.data is XML
//     parseString(response.data, (err: any, result: any) => {
//       if (err) {
//         console.error("XML parse error:", err);
//         return;
//       }
//       const formattedData = JSON.stringify(result, null, 2); // Pretty print JSON
//       fs.writeFileSync("rtmp_stats.json", formattedData); // Save to a file
//       console.log("RTMP stats written to rtmp_stats.json");
//       // result now holds the stats in JS object form
//       // e.g. result.rtmp.server[0].application -> etc.
//       // extract your data, compare with previous poll, and update your DB
//     });
//   } catch (e) {
//     console.error("Stats polling failed:", e);
//   }
// }); // every 5 seconds
